import React, { useState, useEffect, useRef } from 'react';
import { supabase } from '../lib/supabaseClient';
import { verifyPassword, RateLimiter } from '../utils/auth';
import emailService from '../utils/emailService';
import { useAuth } from '../context/AuthContext';
import { useDataSync, SyncStatusIndicator } from '../hooks/useDataSync';
import { adminActivityLogger } from '../utils/adminActivityLogger';
import DeletionManager, { RemovalRequest } from '../utils/deletionManager';

// Rate limiter for login attempts
const loginRateLimiter = new RateLimiter(5, 15 * 60 * 1000); // 5 attempts per 15 minutes
const requestRateLimiter = new RateLimiter(3, 60 * 60 * 1000); // 3 requests per hour

type AdminAccess = {
  id: string;
  client_username: string;
  admin_currently_accessing: boolean;
  admin_session_started_at: string;
  admin_session_ended_at?: string;
  status: string;
  expires_at: string;
};

type Project = {
  id: string;
  name: string;
  description?: string;
  client_username: string;
  created_by_admin: boolean;
  status: 'active' | 'completed' | 'archived';
  created_at: string;
  updated_at: string;
};

const ClientPortal = () => {
  const { clientUser, loginClient, logoutClient, endTemporaryAccess } = useAuth();
  // Login credentials (changed to username-based)
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  // Messaging
  const [messages, setMessages] = useState<any[]>([]);
  const [newMessage, setNewMessage] = useState('');
  // File upload
  const [files, setFiles] = useState<any[]>([]);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  // Request Access form
  const [showRequestForm, setShowRequestForm] = useState(false);
  const [requestForm, setRequestForm] = useState({ name: '', email: '', company: '' });
  const [requestSubmitted, setRequestSubmitted] = useState(false);
  const [requestError, setRequestError] = useState('');
  // Account Recovery form
  const [showAccountRecovery, setShowAccountRecovery] = useState(false);
  const [recoveryType, setRecoveryType] = useState<'password' | 'username'>('password');
  const [resetEmail, setResetEmail] = useState('');
  const [resetSubmitted, setResetSubmitted] = useState(false);
  const [resetError, setResetError] = useState('');

  // Data sync functionality
  const { safeDbOperation } = useDataSync();

  // Check if admin is currently accessing this account
  const [adminCurrentlyAccessing, setAdminCurrentlyAccessing] = useState(false);
  const [adminAccessStartTime, setAdminAccessStartTime] = useState<string | null>(null);
  const [adminAccessEndNotification, setAdminAccessEndNotification] = useState<{
    startTime: string;
    endTime: string;
  } | null>(null);

  // Layout state for expand/collapse functionality
  const [messagesExpanded, setMessagesExpanded] = useState(false);

  // File attachment for messages
  const [messageAttachment, setMessageAttachment] = useState<File | null>(null);

  // Project-related state
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [loadingProjects, setLoadingProjects] = useState(false);

  // File commenting
  const [fileComments, setFileComments] = useState<{[key: string]: string}>({});

  // File upload state
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // Dropdown menu states
  const [openMessageDropdown, setOpenMessageDropdown] = useState<string | null>(null);
  const [openFileDropdown, setOpenFileDropdown] = useState<string | null>(null);

  // Auto-scroll refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const filesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Validate user session against database
  const validateUserSession = async () => {
    if (!clientUser) return false;

    try {
      if (clientUser.isTemporaryAccess) {
        // Validate temporary access session
        if (!clientUser.temporaryAccessId) return false;

        const { data: accessData, error } = await supabase
          .from('temporary_access')
          .select('status, expires_at')
          .eq('id', clientUser.temporaryAccessId)
          .single();

        if (error || !accessData || accessData.status !== 'active') {
          console.log('❌ Temporary access session invalid');
          return false;
        }

        if (new Date(accessData.expires_at) < new Date()) {
          console.log('❌ Temporary access session expired');
          return false;
        }

        return true;
      } else {
        // Validate regular client session
        const { data: userData, error } = await supabase
          .from('users')
          .select('username, is_active')
          .eq('username', clientUser.username)
          .single();

        if (error || !userData || !userData.is_active) {
          console.log('❌ Client user session invalid');
          return false;
        }

        return true;
      }
    } catch (error) {
      console.error('❌ Error validating session:', error);
      return false;
    }
  };

  // Smart logout function that handles both real clients and admin temporary access
  const handleLogout = async () => {
    try {
      if (clientUser?.isTemporaryAccess && clientUser?.temporaryAccessId) {
        // This is admin temporary access - only end the admin session
        console.log('🔚 Admin ending temporary access session');
        await endTemporaryAccess(clientUser.temporaryAccessId);

        // Show notification to the real client that admin access ended
        alert(`Admin access session ended.\nSession duration: ${clientUser.adminSessionStartTime ?
          new Date().toLocaleString() + ' (started: ' + new Date(clientUser.adminSessionStartTime).toLocaleString() + ')' :
          'Unknown duration'}`);

        // Redirect admin back to admin portal
        window.location.href = '/company-portal';
      } else {
        // This is a real client - normal logout
        console.log('🚪 Client logging out normally');
        logoutClient();
      }
    } catch (error) {
      console.error('❌ Error during logout:', error);
      alert('Error ending session. Please try again.');
    }
  };

  // Function to check for expired access sessions and send notifications
  const checkExpiredAccess = async () => {
    try {
      const now = new Date().toISOString();

      // Find expired sessions that haven't been processed yet
      const { data: expiredSessions, error } = await supabase
        .from('temporary_access')
        .select('id, client_username, admin_session_started_at, expires_at, granted_by_client_at')
        .eq('status', 'active')
        .lt('expires_at', now)
        .eq('admin_currently_accessing', true);

      if (error) {
        console.error('❌ Error checking expired sessions:', error);
        return;
      }

      if (expiredSessions && expiredSessions.length > 0) {
        console.log(`🕐 Found ${expiredSessions.length} expired sessions to process`);

        for (const session of expiredSessions) {
          try {
            // Update the session to mark it as expired
            const { error: updateError } = await supabase
              .from('temporary_access')
              .update({
                status: 'expired',
                admin_currently_accessing: false,
                admin_session_ended_at: now
              })
              .eq('id', session.id);

            if (updateError) {
              console.error('❌ Failed to update expired session:', updateError);
              continue;
            }

            // Get client email for notification
            const { data: clientData, error: clientError } = await supabase
              .from('users')
              .select('email')
              .eq('username', session.client_username)
              .single();

            if (clientError || !clientData) {
              console.error('❌ Failed to get client email for expired session:', clientError);
              continue;
            }

            // Get activity summary and send expiration notification email
            const activities = await adminActivityLogger.getSessionActivities(session.id);
            const activitySummary = adminActivityLogger.formatActivitiesForEmail(activities);

            await emailService.sendAdminAccessEndedEmail(
              clientData.email,
              session.client_username,
              session.admin_session_started_at || session.granted_by_client_at,
              now,
              'Admin',
              'Access expired automatically',
              activitySummary
            );

            console.log(`✅ Expiration notification sent for session ${session.id}`);

          } catch (sessionError) {
            console.error(`❌ Failed to process expired session ${session.id}:`, sessionError);
          }
        }
      }
    } catch (error) {
      console.error('❌ Error in checkExpiredAccess:', error);
    }
  };

  // Function to check if admin is currently accessing this account
  const checkAdminAccess = async () => {
    if (!clientUser || clientUser.isTemporaryAccess) {
      console.log('🔍 Skipping admin access check - user is admin or no user');
      return; // Don't check if we ARE the admin accessing
    }

    console.log('🔍 Checking admin access for client:', clientUser.username);

    try {
      // Check for currently active admin access
      const { data: activeAccess, error }: { data: AdminAccess[] | null, error: any } = await supabase
        .from('temporary_access')
        .select('id, client_username, admin_currently_accessing, admin_session_started_at, admin_session_ended_at, expires_at, status')
        .eq('client_username', clientUser.username)
        .eq('status', 'active')
        .gte('expires_at', new Date().toISOString());

      // Also check for recently ended sessions (within last 5 minutes) to show notifications
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000).toISOString();
      const { data: recentlyEnded, error: endedError } = await supabase
        .from('temporary_access')
        .select('admin_session_started_at, admin_session_ended_at')
        .eq('client_username', clientUser.username)
        .eq('status', 'active')
        .eq('admin_currently_accessing', false)
        .not('admin_session_ended_at', 'is', null)
        .gte('admin_session_ended_at', fiveMinutesAgo);

      console.log('🔍 Admin access query result:', { activeAccess, error });
      console.log('🔍 Recently ended sessions:', { recentlyEnded, endedError });

      if (error) {
        console.error('❌ Error checking admin access:', error);
        return;
      }

      // Show notification for recently ended sessions
      if (recentlyEnded && recentlyEnded.length > 0 && !adminAccessEndNotification) {
        const endedSession = recentlyEnded[0]; // Get the most recent one
        if (endedSession.admin_session_started_at && endedSession.admin_session_ended_at) {
          setAdminAccessEndNotification({
            startTime: endedSession.admin_session_started_at,
            endTime: endedSession.admin_session_ended_at
          });

          // Auto-hide notification after 30 seconds
          setTimeout(() => {
            setAdminAccessEndNotification(null);
          }, 30000);
        }
      }

      // Check if any active access has admin_currently_accessing = true
      console.log('🔍 All active access records:', activeAccess);

      if (activeAccess && activeAccess.length > 0) {
        activeAccess.forEach((access, index) => {
          console.log(`🔍 Access record ${index}:`, {
            id: access.id,
            client_username: access.client_username,
            admin_currently_accessing: access.admin_currently_accessing,
            admin_session_started_at: access.admin_session_started_at,
            status: access.status,
            expires_at: access.expires_at
          });
        });
      }

      const currentlyAccessing = activeAccess?.find(access => access.admin_currently_accessing === true);

      console.log('🔍 Currently accessing:', currentlyAccessing);

      if (currentlyAccessing) {
        console.log('🚨 ADMIN IS CURRENTLY ACCESSING - Setting indicator');
        setAdminCurrentlyAccessing(true);
        setAdminAccessStartTime(currentlyAccessing.admin_session_started_at);
      } else {
        console.log('✅ No admin currently accessing');
        setAdminCurrentlyAccessing(false);
        setAdminAccessStartTime(null);
      }
    } catch (error) {
      console.error('❌ Error checking admin access:', error);
    }
  };

  // Pending/failed operations for retry
  const [pendingMessage, setPendingMessage] = useState<string>('');
  const [pendingFile, setPendingFile] = useState<File | null>(null);
  const [retryingFile, setRetryingFile] = useState(false);

  // Removal requests management
  const [pendingRemovalRequests, setPendingRemovalRequests] = useState<RemovalRequest[]>([]);
  const [completedRequests, setCompletedRequests] = useState<RemovalRequest[]>([]);

  // Reason popup management
  const [showReasonPopup, setShowReasonPopup] = useState(false);
  const [selectedReason, setSelectedReason] = useState<string>('');

  // Temporary access management
  const [showAccessDialog, setShowAccessDialog] = useState(false);
  const [accessDays, setAccessDays] = useState(1);
  const [grantingAccess, setGrantingAccess] = useState(false);
  const [currentAccess, setCurrentAccess] = useState<any>(null);
  const [recentlyRevokedAccess, setRecentlyRevokedAccess] = useState<any>(null);

  const handleLogin = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    // Check rate limiting
    const clientIP = 'client'; // In production, get real IP
    if (loginRateLimiter.isRateLimited(clientIP)) {
      const timeLeft = Math.ceil(loginRateLimiter.getTimeUntilReset(clientIP) / 1000 / 60);
      setError(`Too many login attempts. Please try again in ${timeLeft} minutes.`);
      setLoading(false);
      return;
    }

    try {
      // Query the users table for authentication
      const { data: users, error } = await supabase
        .from('users')
        .select('*')
        .eq('username', username)
        .eq('is_active', true)
        .limit(1);

      if (error) {
        setError('Login failed: ' + error.message);
        setLoading(false);
        return;
      }

      if (users && users.length > 0) {
        const user = users[0];

        // Verify password (supports both hashed and plain text for migration)
        let passwordValid = false;
        if (user.password_hash.startsWith('$2')) {
          // Hashed password
          passwordValid = await verifyPassword(password, user.password_hash);
        } else {
          // Plain text password (legacy support)
          passwordValid = user.password_hash === password;
        }

        if (passwordValid) {
          // Update last_login timestamp
          await supabase
            .from('users')
            .update({ last_login: new Date().toISOString() })
            .eq('id', user.id);

          // Use AuthContext client login
          loginClient({
            id: user.id,
            username: user.username,
            email: user.email,
            name: user.name,
            company: user.company,
            type: 'client'
          });
        } else {
          setError('Invalid username or password');
        }
      } else {
        setError('Invalid username or password');
      }
    } catch (err) {
      setError('An error occurred during login. Please try again.');
      console.error('Login error:', err);
    }

    setLoading(false);
  };

  const handleRequestChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setRequestForm({ ...requestForm, [e.target.name]: e.target.value });
  };

  const handleAccountRecovery = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);
    setResetError('');

    try {
      // Check if user exists
      const { data: users, error } = await supabase
        .from('users')
        .select('id, username, email, name')
        .eq('email', resetEmail)
        .eq('is_active', true)
        .limit(1);

      if (error) {
        setResetError('Error checking user: ' + error.message);
        setLoading(false);
        return;
      }

      if (!users || users.length === 0) {
        setResetError('No account found with this email address.');
        setLoading(false);
        return;
      }

      const user = users[0];

      if (recoveryType === 'password') {
        // Generate a temporary password
        const tempPassword = Math.random().toString(36).slice(-8) + Math.random().toString(36).slice(-4).toUpperCase();
        const hashedTempPassword = await import('../utils/auth').then(auth => auth.hashPassword(tempPassword));

        // Update user's password in database
        const { error: updateError } = await supabase
          .from('users')
          .update({ password_hash: hashedTempPassword })
          .eq('id', user.id);

        if (updateError) {
          setResetError('Failed to reset password: ' + updateError.message);
          setLoading(false);
          return;
        }

        // Send email with temporary password
        try {
          console.log('Sending password reset email...');
          const emailSent = await emailService.sendPasswordResetEmail(
            resetEmail,
            tempPassword
          );

          if (!emailSent) {
            console.error('Password reset email service returned false');
            setResetError('Failed to send password reset email. Please try again or contact support.');
            setLoading(false);
            return;
          } else {
            console.log('Password reset email sent successfully');
          }
        } catch (emailError) {
          console.error('Password reset email failed:', emailError);
          setResetError('Failed to send password reset email. Please try again or contact support.');
          setLoading(false);
          return;
        }
      } else {
        // Username recovery
        try {
          console.log('Sending username recovery email...');
          const emailSent = await emailService.sendUsernameRecoveryEmail(
            resetEmail,
            user.username
          );

          if (!emailSent) {
            console.error('Username recovery email service returned false');
            setResetError('Failed to send username recovery email. Please try again or contact support.');
            setLoading(false);
            return;
          } else {
            console.log('Username recovery email sent successfully');
          }
        } catch (emailError) {
          console.error('Username recovery email failed:', emailError);
          setResetError('Failed to send username recovery email. Please try again or contact support.');
          setLoading(false);
          return;
        }
      }

      setResetSubmitted(true);
      setResetEmail('');
      setShowAccountRecovery(false);

    } catch (err) {
      setResetError(`An error occurred during ${recoveryType} recovery. Please try again.`);
      console.error(`${recoveryType} recovery error:`, err);
    } finally {
      setLoading(false);
    }
  };

  const handleRequestSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);
    setRequestError('');

    // Check rate limiting for access requests
    const requestIP = requestForm.email; // Use email as identifier
    if (requestRateLimiter.isRateLimited(requestIP)) {
      const timeLeft = Math.ceil(requestRateLimiter.getTimeUntilReset(requestIP) / 1000 / 60);
      setRequestError(`Too many requests. Please try again in ${timeLeft} minutes.`);
      setLoading(false);
      return;
    }

    try {
      // Submit access request via backend API (which handles database + email)
      try {
        console.log('Submitting access request via backend...');
        const requestSubmitted = await emailService.submitAccessRequest(
          requestForm.name,
          requestForm.email,
          requestForm.company
        );

        if (!requestSubmitted) {
          console.error('Access request service returned false');
          setRequestError('Failed to submit access request. Please try again or contact support.');
          return;
        } else {
          console.log('Access request submitted successfully');
          setRequestSubmitted(true);
          setRequestForm({ name: '', email: '', company: '' });
          setShowRequestForm(false);
        }
      } catch (requestError) {
        console.error('Access request submission failed:', requestError);
        setRequestError('Failed to submit access request. Please try again or contact support.');
        return;
      }
    } catch (err) {
      setRequestError('An unexpected error occurred. Please try again.');
      console.error('Access request submission error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (clientUser) {
      fetchProjects();
      fetchMessages();
      fetchFiles();
      fetchRemovalRequests();
      fetchCurrentAccess();
      checkAdminAccess(); // Check if admin is currently accessing
    }
  }, [clientUser]);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Auto-scroll to bottom when files change
  useEffect(() => {
    if (filesEndRef.current) {
      filesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [files]);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.dropdown-container')) {
        setOpenMessageDropdown(null);
        setOpenFileDropdown(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Refetch messages and files when selected project changes
  useEffect(() => {
    if (selectedProject && clientUser) {
      fetchMessages();
      fetchFiles();
    }
  }, [selectedProject]);

  // Function to check if current admin temporary access is still valid
  const checkTemporaryAccessValidity = async () => {
    if (!clientUser?.isTemporaryAccess || !clientUser?.temporaryAccessId) return;

    try {
      console.log('🔍 Checking temporary access validity for admin session');

      const { data: accessData, error } = await supabase
        .from('temporary_access')
        .select('status, revoked_at, revoked_by, expires_at')
        .eq('id', clientUser.temporaryAccessId)
        .single();

      if (error) {
        console.error('❌ Error checking temporary access validity:', error);
        return;
      }

      if (!accessData) {
        console.log('❌ Temporary access record not found - logging out admin');
        alert('❌ Your temporary access session is no longer valid. You will be logged out.');
        await logoutClient();
        return;
      }

      // Check if access has been revoked
      if (accessData.status === 'revoked') {
        console.log('❌ Temporary access has been revoked - logging out admin');
        const revokedBy = accessData.revoked_by === 'client' ? 'the client' : 'admin';
        alert(`❌ Your temporary access has been revoked by ${revokedBy}. You will be logged out.`);
        await logoutClient();
        return;
      }

      // Check if access has expired
      const now = new Date().toISOString();
      if (accessData.expires_at && accessData.expires_at < now) {
        console.log('❌ Temporary access has expired - logging out admin');
        alert('❌ Your temporary access has expired. You will be logged out.');
        await logoutClient();
        return;
      }

      console.log('✅ Temporary access is still valid');
    } catch (error) {
      console.error('❌ Error checking temporary access validity:', error);
    }
  };

  // Validate session on mount and periodically
  useEffect(() => {
    if (!clientUser) return;

    // Validate session immediately on mount
    const validateAndHandle = async () => {
      const isValid = await validateUserSession();
      if (!isValid) {
        console.log('❌ Session validation failed - logging out');
        alert('❌ Your session is no longer valid. You will be logged out.');
        logoutClient();
        window.location.href = '/client-portal';
        return;
      }
    };

    validateAndHandle();

    // Set up periodic checks
    const interval = setInterval(async () => {
      if (clientUser.isTemporaryAccess) {
        // If admin is in temporary access mode, check if access is still valid
        checkTemporaryAccessValidity();
      } else {
        // If real client, check for admin access and expired sessions
        checkAdminAccess();
        checkExpiredAccess();

        // Also validate session periodically
        const isValid = await validateUserSession();
        if (!isValid) {
          console.log('❌ Periodic session validation failed - logging out');
          alert('❌ Your session is no longer valid. You will be logged out.');
          logoutClient();
          window.location.href = '/client-portal';
          return;
        }
      }
    }, 10000); // Check every 10 seconds

    return () => clearInterval(interval);
  }, [clientUser]);

  // Fetch current temporary access status
  const fetchCurrentAccess = async () => {
    if (!clientUser) return;

    try {
      // Fetch active access
      const { data, error } = await supabase
        .from('temporary_access')
        .select('*')
        .eq('client_username', clientUser.username)
        .eq('status', 'active')
        .gt('expires_at', new Date().toISOString())
        .order('created_at', { ascending: false })
        .limit(1);

      if (error) throw error;
      setCurrentAccess(data && data.length > 0 ? data[0] : null);

      // Also fetch recently revoked access (within last 24 hours) to show who revoked it
      const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
      const { data: revokedData, error: revokedError } = await supabase
        .from('temporary_access')
        .select('*')
        .eq('client_username', clientUser.username)
        .eq('status', 'revoked')
        .gte('revoked_at', twentyFourHoursAgo)
        .order('revoked_at', { ascending: false })
        .limit(1);

      if (!revokedError && revokedData && revokedData.length > 0) {
        setRecentlyRevokedAccess(revokedData[0]);
      } else {
        setRecentlyRevokedAccess(null);
      }

    } catch (error) {
      console.error('Error fetching current access:', error);
    }
  };

  // Fetch messages for this client (excluding deleted ones)
  const fetchMessages = async () => {
    if (!clientUser || !selectedProject) return;

    try {
      const { data, error } = await supabase
        .from('messages')
        .select('*')
        .or(`sender_username.eq.${clientUser.username},recipient_username.eq.${clientUser.username}`)
        .eq('project_id', selectedProject.id)
        .order('created_at', { ascending: true });

      if (error) throw error;

      // Filter out messages deleted by current user
      const visibleMessages = (data || []).filter(msg => {
        if (msg.sender_username === clientUser.username) {
          return !msg.deleted_by_sender;
        } else {
          return !msg.deleted_by_recipient;
        }
      });

      console.log('Fetched messages:', visibleMessages);
      setMessages(visibleMessages);

      // Mark admin messages as read by client
      if (data && data.length > 0) {
        const unreadAdminMessages = data.filter(msg =>
          msg.sender_username === 'admin' &&
          msg.recipient_username === clientUser.username &&
          !msg.read_by_client
        );

        if (unreadAdminMessages.length > 0) {
          await supabase
            .from('messages')
            .update({ read_by_client: true })
            .eq('sender_username', 'admin')
            .eq('recipient_username', clientUser.username)
            .eq('read_by_client', false);
        }
      }
    } catch (error) {
      console.error('Error fetching messages:', error);
    }
  };

  // Fetch files for this client (excluding deleted ones)
  const fetchFiles = async () => {
    if (!clientUser || !selectedProject) return;

    try {
      // Fetch files uploaded by client OR files uploaded by admin for this client
      const { data, error } = await supabase
        .from('files')
        .select('*')
        .or(`uploader_username.eq.${clientUser.username},target_username.eq.${clientUser.username}`)
        .eq('project_id', selectedProject.id)
        .order('created_at', { ascending: true });

      if (error) throw error;

      // Filter out files deleted by current user
      const visibleFiles = (data || []).filter(file => {
        if (file.uploader_username === clientUser.username) {
          return !file.deleted_by_uploader;
        } else if (file.target_username === clientUser.username) {
          return !file.deleted_by_target;
        }
        return true;
      });

      console.log('Fetched files:', visibleFiles);
      setFiles(visibleFiles);

      // Mark admin files as read by client
      if (data && data.length > 0) {
        const unreadAdminFiles = data.filter(file =>
          file.uploaded_by_admin &&
          file.target_username === clientUser.username &&
          !file.read_by_client
        );

        if (unreadAdminFiles.length > 0) {
          await supabase
            .from('files')
            .update({ read_by_client: true })
            .eq('uploaded_by_admin', true)
            .eq('target_username', clientUser.username)
            .eq('read_by_client', false);
        }
      }
    } catch (error) {
      console.error('Error fetching files:', error);
    }
  };

  // Fetch projects for this client
  const fetchProjects = async () => {
    if (!clientUser) return;

    try {
      setLoadingProjects(true);
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .eq('client_username', clientUser.username)
        .eq('status', 'active')
        .order('created_at', { ascending: true });

      if (error) throw error;

      setProjects(data || []);

      // Auto-select first project if none selected
      if (data && data.length > 0 && !selectedProject) {
        setSelectedProject(data[0]);
      }
    } catch (error) {
      console.error('Error fetching projects:', error);
    } finally {
      setLoadingProjects(false);
    }
  };

  // Add comment to file
  const addFileComment = async (fileId: string) => {
    const comment = fileComments[fileId];
    if (!comment?.trim()) return;

    try {
      const { error } = await supabase
        .from('files')
        .update({ client_comment: comment })
        .eq('id', fileId);

      if (error) throw error;

      // Clear comment input
      setFileComments(prev => ({ ...prev, [fileId]: '' }));

      // Refresh files
      await fetchFiles();
    } catch (error) {
      console.error('Error adding file comment:', error);
    }
  };

  // Copy message content to clipboard
  const copyMessage = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      alert('✅ Message copied to clipboard!');
    } catch (error) {
      console.error('Failed to copy message:', error);
      alert('❌ Failed to copy message');
    }
  };

  // Copy file to clipboard (actual file data)
  const copyFile = async (file: any) => {
    try {
      // Fetch the actual file data
      const response = await fetch(file.url);
      if (!response.ok) {
        throw new Error('Failed to fetch file');
      }

      const blob = await response.blob();

      // Create a File object from the blob
      const fileObj = new File([blob], file.filename, { type: blob.type });

      // Try to copy to clipboard using the modern Clipboard API
      if (navigator.clipboard && navigator.clipboard.write) {
        const clipboardItem = new ClipboardItem({
          [blob.type]: blob
        });

        await navigator.clipboard.write([clipboardItem]);
        alert('✅ File copied to clipboard! You can now paste it in other applications.');
      } else {
        // Fallback: copy file URL/name if clipboard API doesn't support files
        const textToCopy = file.url || file.filename;
        await navigator.clipboard.writeText(textToCopy);
        alert('✅ File URL copied to clipboard!');
      }
    } catch (error) {
      console.error('Failed to copy file:', error);
      // Fallback to copying file URL
      try {
        const textToCopy = file.url || file.filename;
        await navigator.clipboard.writeText(textToCopy);
        alert('✅ File URL copied to clipboard!');
      } catch (fallbackError) {
        alert('❌ Failed to copy file');
      }
    }
  };

  // Request deletion from admin
  const requestDeletion = async (itemType: 'message' | 'file', itemId: string, reason?: string) => {
    if (!clientUser) return;

    try {
      const { error } = await supabase
        .from('removal_requests')
        .insert([{
          requester_username: clientUser.username,
          target_username: 'admin',
          item_type: itemType,
          item_id: itemId,
          reason: reason || '',
          status: 'pending'
        }]);

      if (error) throw error;

      await fetchRemovalRequests();
      alert('✅ Deletion request sent to admin!');
    } catch (error) {
      console.error('Error requesting deletion:', error);
      alert('❌ Failed to send deletion request');
    }
  };

  // Send message to database (STRICT - only succeed if database confirms)
  const sendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    // Determine if this is a retry or new message
    const isRetry = !!pendingMessage;
    const messageToSend = isRetry ? pendingMessage : newMessage;
    const attachmentToSend = isRetry ? null : messageAttachment;

    if ((!messageToSend.trim() && !attachmentToSend) || !clientUser) return;

    // Show loading state
    const form = e.target as HTMLFormElement;
    const sendButton = form.querySelector('button[type="submit"]') as HTMLButtonElement;
    if (sendButton) {
      sendButton.disabled = true;
      sendButton.textContent = isRetry ? 'Retrying...' : 'Sending...';
    }

    try {
      let fileUrl = '';
      let originalFileName = '';

      // Upload file first if there's an attachment (using same approach as existing file upload)
      if (attachmentToSend) {
        originalFileName = attachmentToSend.name;
        fileUrl = `/uploads/${originalFileName}`; // Placeholder URL like existing system

        // Save file record to database using same structure as existing handleFileUpload
        const { error: fileDbError } = await supabase
          .from('files')
          .insert([{
            uploader_username: clientUser.username,
            filename: originalFileName,
            file_size: attachmentToSend.size,
            file_type: attachmentToSend.type,
            url: fileUrl,
            reviewed_by_admin: false,
            uploaded_by_admin: clientUser.isTemporaryAccess || false,
            target_username: null,
            read_by_client: true,
            project_id: selectedProject?.id,
            created_at: new Date().toISOString()
          }]);

        if (fileDbError) {
          throw new Error(`File database record failed: ${fileDbError.message}`);
        }
      }

      // Prepare message content with file link if attached
      let finalMessageContent = messageToSend;
      if (fileUrl && originalFileName) {
        finalMessageContent = messageToSend
          ? `${messageToSend}\n\n📎 Attached file: ${originalFileName}`
          : `📎 Attached file: ${originalFileName}`;
      }

      // STRICT DATABASE OPERATION - No fallback to cache for critical data
      const { data, error } = await supabase
        .from('messages')
        .insert([{
          sender_username: clientUser.username,
          recipient_username: 'admin',
          content: finalMessageContent,
          read_by_admin: false,
          read_by_client: true,
          project_id: selectedProject?.id,
          created_at: new Date().toISOString()
        }])
        .select();

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      if (!data || data.length === 0) {
        throw new Error('Message was not saved to database');
      }

      console.log('✅ Message confirmed in database:', data[0]);

      // Log admin activity if this is an admin session
      if (clientUser.isTemporaryAccess) {
        await adminActivityLogger.logMessageSent(messageToSend, data[0].id);
      }

      // Clear both current and pending message on success
      setNewMessage('');
      setPendingMessage('');
      setMessageAttachment(null);

      await fetchMessages(); // Refresh messages
      await fetchFiles(); // Refresh files if attachment was uploaded

      // Show success feedback
      alert('✅ Message sent successfully!');

    } catch (error) {
      console.error('❌ Message send failed:', error);

      // Store message for retry if not already pending
      if (!isRetry) {
        setPendingMessage(messageToSend);
      }

      // Show clear error to user with retry option
      const retryMessage = isRetry ? 'Retry failed again.' : 'Message saved for retry.';
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      alert(`❌ FAILED TO SEND MESSAGE\n\nError: ${errorMessage}\n\n${retryMessage} Use the retry button to try again when connection is stable.`);

    } finally {
      // Reset button state
      if (sendButton) {
        sendButton.disabled = false;
        sendButton.textContent = pendingMessage ? 'Retry' : 'Send';
      }
    }
  };



  // Handle file upload to database (STRICT - only succeed if database confirms)
  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>, isRetry = false) => {
    const file = isRetry ? pendingFile : e.target.files?.[0];
    if (!file || !clientUser) return;

    setUploading(isRetry ? false : true);
    if (isRetry) setRetryingFile(true);

    try {
      // STRICT DATABASE OPERATION - No fallback to cache for critical data
      const { data, error } = await supabase
        .from('files')
        .insert([{
          uploader_username: clientUser.username,
          filename: file.name,
          file_size: file.size,
          file_type: file.type,
          url: `/uploads/${file.name}`, // Placeholder - would be actual storage URL
          reviewed_by_admin: false,
          uploaded_by_admin: false,
          target_username: null, // Not applicable for client uploads
          read_by_client: true, // Client has "seen" their own upload
          project_id: selectedProject?.id,
          created_at: new Date().toISOString()
        }])
        .select();

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      if (!data || data.length === 0) {
        throw new Error('File was not saved to database');
      }

      console.log('✅ File confirmed in database:', data[0]);

      // Log admin activity if this is an admin session
      if (clientUser.isTemporaryAccess) {
        await adminActivityLogger.logFileUploaded(file.name, file.size, data[0].id);
      }

      await fetchFiles(); // Refresh files

      // Clear the file input and pending file on success
      if (!isRetry && e.target) e.target.value = '';
      setPendingFile(null);

      // Show success feedback
      alert(`✅ File "${file.name}" uploaded successfully!`);

    } catch (error) {
      console.error('❌ File upload failed:', error);

      // Store file for retry if not already pending
      if (!isRetry) {
        setPendingFile(file);
      }

      // Show clear error to user with retry option
      const retryMessage = isRetry ? 'Retry failed again.' : 'File saved for retry.';
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      alert(`❌ FAILED TO UPLOAD FILE\n\nFile: ${file.name}\nError: ${errorMessage}\n\n${retryMessage} Use the retry button to try again when connection is stable.`);

      // Don't clear the file input on failure so user can retry

    } finally {
      setUploading(false);
      setRetryingFile(false);
    }
  };

  // Retry pending file
  const retryFile = async () => {
    if (!pendingFile || !clientUser) return;

    const fakeEvent = {
      target: null
    } as any;

    await handleFileUpload(fakeEvent, true);
  };

  // Delete message from current user's view or for everyone
  const deleteMessage = async (messageId: string, forEveryone: boolean = false) => {
    if (!clientUser) return;

    // Prevent admin from deleting during temporary access
    if (clientUser.isTemporaryAccess) {
      alert('❌ Cannot delete messages during admin temporary access');
      return;
    }

    // Check if there's a pending deletion request for this message
    const incomingRequest = getPendingRequestForItem('message', messageId);

    let confirmed: boolean;
    let success: boolean;

    if (forEveryone) {
      confirmed = window.confirm('Delete this message for everyone? This cannot be undone.');
      if (!confirmed) return;

      success = await DeletionManager.deleteMessageForEveryone(messageId, clientUser.username);
      if (success) {
        // If there was a pending deletion request, auto-approve it
        if (incomingRequest) {
          await DeletionManager.respondToRemovalRequest(incomingRequest.id, 'approved', clientUser.username);
        }
        await fetchMessages();
        await fetchRemovalRequests();
        alert('✅ Message deleted for everyone');
      } else {
        alert('❌ Failed to delete message');
      }
    } else {
      confirmed = window.confirm('Delete this message from your view? The other person will still see it unless they delete it too.');
      if (!confirmed) return;

      success = await DeletionManager.deleteMessageFromMyView(messageId, clientUser.username);
      if (success) {
        // If there was a pending deletion request, auto-approve it since the message was deleted
        if (incomingRequest) {
          // Auto-approve the deletion request since the message was deleted
          await DeletionManager.respondToRemovalRequest(incomingRequest.id, 'approved', clientUser.username);
          // Also delete for everyone since the request was to remove it from both sides
          await DeletionManager.deleteMessageForEveryone(messageId, clientUser.username);
        }

        await fetchMessages();
        await fetchRemovalRequests();
        alert('✅ Message deleted from your view');
      } else {
        alert('❌ Failed to delete message');
      }
    }
  };

  // Delete file from current user's view or for everyone
  const deleteFile = async (fileId: string, forEveryone: boolean = false) => {
    if (!clientUser) return;

    // Prevent admin from deleting during temporary access
    if (clientUser.isTemporaryAccess) {
      alert('❌ Cannot delete files during admin temporary access');
      return;
    }

    // Check if there's a pending deletion request for this file
    const incomingRequest = getPendingRequestForItem('file', fileId);

    let confirmed: boolean;
    let success: boolean;

    if (forEveryone) {
      confirmed = window.confirm('Delete this file for everyone? This cannot be undone.');
      if (!confirmed) return;

      success = await DeletionManager.deleteFileForEveryone(fileId, clientUser.username);
      if (success) {
        // If there was a pending deletion request, auto-approve it
        if (incomingRequest) {
          await DeletionManager.respondToRemovalRequest(incomingRequest.id, 'approved', clientUser.username);
        }
        await fetchFiles();
        await fetchRemovalRequests();
        alert('✅ File deleted for everyone');
      } else {
        alert('❌ Failed to delete file');
      }
    } else {
      confirmed = window.confirm('Delete this file from your view? The other person will still see it unless they delete it too.');
      if (!confirmed) return;

      success = await DeletionManager.deleteFileFromMyView(fileId, clientUser.username);
      if (success) {
        // If there was a pending deletion request, auto-approve it since the file was deleted
        if (incomingRequest) {
          // Auto-approve the deletion request since the file was deleted
          await DeletionManager.respondToRemovalRequest(incomingRequest.id, 'approved', clientUser.username);
          // Also delete for everyone since the request was to remove it from both sides
          await DeletionManager.deleteFileForEveryone(fileId, clientUser.username);
        }

        await fetchFiles();
        await fetchRemovalRequests();
        alert('✅ File deleted from your view');
      } else {
        alert('❌ Failed to delete file');
      }
    }
  };

  // Send removal request to other party
  const sendRemovalRequest = async (itemType: 'message' | 'file', itemId: string, targetUsername: string) => {
    if (!clientUser) return;

    // Prevent admin from sending removal requests during temporary access
    if (clientUser.isTemporaryAccess) {
      alert('❌ Cannot send removal requests during admin temporary access');
      return;
    }

    // Check if user can still make requests for this item
    const eligibility = await DeletionManager.canMakeRemovalRequest(itemType, itemId, clientUser.username);

    if (!eligibility.canRequest) {
      alert(`❌ ${eligibility.reason || 'Cannot make more requests for this item'}`);
      return;
    }

    const reason = prompt(`Optional: Why do you want the other person to also remove this ${itemType}?\n\n(${eligibility.remainingRequests} requests remaining after this one)`);

    const success = await DeletionManager.sendRemovalRequest(
      itemType,
      itemId,
      clientUser.username,
      targetUsername,
      reason || undefined
    );

    if (success) {
      fetchRemovalRequests(); // Refresh to show updated state
      alert('✅ Removal request sent');
    } else {
      alert('❌ Failed to send removal request');
    }
  };

  // Check if there's a pending removal request for an item (incoming)
  const getPendingRequestForItem = (itemType: 'message' | 'file', itemId: string) => {
    if (!clientUser) return undefined;
    return pendingRemovalRequests.find(req =>
      req.item_type === itemType &&
      req.item_id === itemId &&
      req.status === 'pending' &&
      req.target_username === clientUser.username
    );
  };

  // Check if there's an outgoing removal request for an item
  const getOutgoingRequestForItem = (itemType: 'message' | 'file', itemId: string) => {
    if (!clientUser) return undefined;
    return pendingRemovalRequests.find(req =>
      req.item_type === itemType &&
      req.item_id === itemId &&
      req.status === 'pending' &&
      req.requester_username === clientUser.username
    );
  };

  // Fetch pending removal requests
  const fetchRemovalRequests = async () => {
    if (!clientUser) return;

    const requests = await DeletionManager.getPendingRemovalRequests(clientUser.username);
    setPendingRemovalRequests(requests);

    // Also fetch completed requests for notifications
    const completed = await DeletionManager.getRecentCompletedRequests(clientUser.username);
    setCompletedRequests(completed);
  };

  // Respond to removal request
  const respondToRemovalRequest = async (requestId: string, response: 'approved' | 'denied') => {
    if (!clientUser) return;

    const result = await DeletionManager.respondToRemovalRequest(requestId, response, clientUser.username);
    if (result.success) {
      await fetchRemovalRequests();
      await fetchMessages();
      await fetchFiles();

      const actionText = response === 'approved' ? 'approved and item removed' : 'denied';
      const requesterText = result.requesterUsername === 'admin' ? 'Admin' : result.requesterUsername;
      alert(`✅ Deletion request from ${requesterText} ${actionText}. They have been notified.`);
    } else {
      alert('❌ Failed to respond to request');
    }
  };

  // Grant temporary access to admin
  const grantTemporaryAccess = async () => {
    if (!clientUser) return;

    setGrantingAccess(true);

    try {
      // Generate unique access token
      const accessToken = crypto.randomUUID();
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + accessDays);

      const { data, error } = await supabase
        .from('temporary_access')
        .insert([{
          client_username: clientUser.username,
          expires_at: expiresAt.toISOString(),
          access_duration_days: accessDays,
          access_token: accessToken,
          status: 'active'
        }])
        .select();

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      if (!data || data.length === 0) {
        throw new Error('Access grant was not saved to database');
      }

      console.log('✅ Temporary access granted:', data[0]);

      // Refresh current access
      await fetchCurrentAccess();

      // Close dialog
      setShowAccessDialog(false);

      // Show success message
      alert(`✅ Temporary access granted to admin for ${accessDays} days!\n\nAdmin can now access your account until ${expiresAt.toLocaleDateString()}.`);

    } catch (error) {
      console.error('❌ Failed to grant access:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      alert(`❌ FAILED TO GRANT ACCESS\n\nError: ${errorMessage}\n\nPlease try again when the connection is stable.`);
    } finally {
      setGrantingAccess(false);
    }
  };

  // Revoke current temporary access
  const revokeTemporaryAccess = async () => {
    if (!currentAccess || !clientUser) return;

    if (!confirm('Are you sure you want to revoke admin access to your account?')) {
      return;
    }

    try {
      const revokedAt = new Date().toISOString();

      const { error } = await supabase
        .from('temporary_access')
        .update({
          status: 'revoked',
          revoked_at: revokedAt,
          revoked_by: 'client',
          admin_currently_accessing: false,
          admin_session_ended_at: revokedAt
        })
        .eq('id', currentAccess.id);

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      console.log('✅ Temporary access revoked');

      // Send email notification about revocation with activity summary
      try {
        const activities = await adminActivityLogger.getSessionActivities(currentAccess.id);
        const activitySummary = adminActivityLogger.formatActivitiesForEmail(activities);

        await emailService.sendAdminAccessEndedEmail(
          clientUser.email,
          clientUser.username,
          currentAccess.admin_session_started_at || currentAccess.granted_by_client_at,
          revokedAt,
          'Admin',
          'Access revoked by client',
          activitySummary
        );
        console.log('✅ Access revocation email sent with activity summary');
      } catch (emailError) {
        console.error('❌ Failed to send revocation email:', emailError);
        // Don't block the revocation if email fails
      }

      // Refresh current access
      await fetchCurrentAccess();

      alert('✅ Admin access has been revoked successfully.');

    } catch (error) {
      console.error('❌ Failed to revoke access:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      alert(`❌ FAILED TO REVOKE ACCESS\n\nError: ${errorMessage}\n\nPlease try again.`);
    }
  };
  if (!clientUser) {
    return (
      <div className="max-w-sm mx-auto p-6 bg-gray-900 rounded-lg shadow text-xs mt-8">
        <h1 className="text-white text-xl font-bold mb-4 text-center">Client Portal Login</h1>

        {requestSubmitted ? (
          <div className="bg-green-900 text-green-300 text-center rounded p-4 mb-4 text-sm">
            Access request submitted! Please check your email and click the verification link to complete your request. After verification, our team will review your application.
          </div>
        ) : resetSubmitted ? (
          <div className="bg-green-900 text-green-300 text-center rounded p-4 mb-4 text-sm">
            {recoveryType === 'password'
              ? 'Password reset email sent! Please check your email for temporary login credentials.'
              : 'Username recovery email sent! Please check your email for your username.'
            }
          </div>
        ) : !showRequestForm && !showAccountRecovery ? (
          <>
            <form onSubmit={handleLogin} className="space-y-4">
              <input
                type="text"
                placeholder="Username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="w-full px-3 py-2 rounded bg-gray-800 text-gray-100 border border-gray-700"
                required
              />
              <input
                type="password"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-3 py-2 rounded bg-gray-800 text-gray-100 border border-gray-700"
                required
              />
              <button
                type="submit"
                disabled={loading}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg font-semibold shadow hover:bg-blue-700 transition disabled:opacity-50"
              >
                {loading ? 'Logging in...' : 'Login'}
              </button>
            </form>
            {error && <div className="text-red-400 text-center mt-2">{error}</div>}

            <div className="mt-4 pt-4 border-t border-gray-700 space-y-2">
              <button
                onClick={() => setShowRequestForm(true)}
                className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg font-semibold shadow hover:bg-gray-700 transition text-sm"
              >
                Request Access
              </button>
              <button
                onClick={() => setShowAccountRecovery(true)}
                className="w-full px-4 py-2 bg-gray-500 text-white rounded-lg font-semibold shadow hover:bg-gray-600 transition text-xs"
              >
                Forgot Username/Password?
              </button>
              <div className="text-gray-400 text-center mt-2 text-xs">
                Don't have an account? Request access above.
              </div>
            </div>

            <div className="text-gray-400 text-center mt-4 text-xs">
              Demo login: demo_user / SODA
            </div>
          </>
        ) : showAccountRecovery ? (
          <div>
            <h2 className="text-white text-base mb-4 font-bold">Account Recovery</h2>

            {/* Recovery Type Selection */}
            <div className="mb-4">
              <label className="block text-gray-300 text-xs mb-2">What do you need to recover?</label>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="recoveryType"
                    value="password"
                    checked={recoveryType === 'password'}
                    onChange={(e) => setRecoveryType(e.target.value as 'password' | 'username')}
                    className="mr-2"
                  />
                  <span className="text-gray-300 text-xs">Password (I know my username)</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="recoveryType"
                    value="username"
                    checked={recoveryType === 'username'}
                    onChange={(e) => setRecoveryType(e.target.value as 'password' | 'username')}
                    className="mr-2"
                  />
                  <span className="text-gray-300 text-xs">Username (I forgot my username)</span>
                </label>
              </div>
            </div>

            <form onSubmit={handleAccountRecovery} className="space-y-3">
              <input
                type="email"
                placeholder="Enter your email address"
                value={resetEmail}
                onChange={(e) => setResetEmail(e.target.value)}
                className="w-full px-3 py-2 rounded bg-gray-800 text-gray-100 border border-gray-700 text-xs"
                required
              />
              <div className="flex gap-2">
                <button
                  type="submit"
                  disabled={loading}
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg font-semibold shadow hover:bg-blue-700 transition disabled:opacity-50 text-xs"
                >
                  {loading ? 'Sending...' : `Recover ${recoveryType === 'password' ? 'Password' : 'Username'}`}
                </button>
                <button
                  type="button"
                  onClick={() => setShowAccountRecovery(false)}
                  className="flex-1 px-4 py-2 bg-gray-600 text-white rounded-lg font-semibold shadow hover:bg-gray-700 transition text-xs"
                >
                  Cancel
                </button>
              </div>
            </form>
            {resetError && <div className="text-red-400 text-center mt-2 text-xs">{resetError}</div>}
            <div className="text-gray-400 text-center mt-3 text-xs">
              Enter your email address and we'll send you a temporary password.
            </div>
          </div>
        ) : (
          <div>
            <h2 className="text-white text-base mb-4 font-bold">Request Portal Access</h2>
            <form onSubmit={handleRequestSubmit} className="space-y-3">
              <input
                type="text"
                name="name"
                placeholder="Full Name"
                value={requestForm.name}
                onChange={handleRequestChange}
                className="w-full px-3 py-2 rounded bg-gray-800 text-gray-100 border border-gray-700 text-xs"
                required
              />
              <input
                type="email"
                name="email"
                placeholder="Email Address"
                value={requestForm.email}
                onChange={handleRequestChange}
                className="w-full px-3 py-2 rounded bg-gray-800 text-gray-100 border border-gray-700 text-xs"
                required
              />
              <input
                type="text"
                name="company"
                placeholder="Company Name"
                value={requestForm.company}
                onChange={handleRequestChange}
                className="w-full px-3 py-2 rounded bg-gray-800 text-gray-100 border border-gray-700 text-xs"
                required
              />
              <div className="flex gap-2">
                <button
                  type="submit"
                  disabled={loading}
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg font-semibold shadow hover:bg-blue-700 transition disabled:opacity-50 text-xs"
                >
                  {loading ? 'Submitting...' : 'Submit Request'}
                </button>
                <button
                  type="button"
                  onClick={() => setShowRequestForm(false)}
                  className="flex-1 px-4 py-2 bg-gray-600 text-white rounded-lg font-semibold shadow hover:bg-gray-700 transition text-xs"
                >
                  Cancel
                </button>
              </div>
            </form>
            {requestError && <div className="text-red-400 text-center mt-2 text-xs">{requestError}</div>}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-800 overflow-y-auto">
      <div className="w-full p-4 bg-gray-900 text-xs">
      {/* Temporary Access Indicators */}
      {clientUser.isTemporaryAccess && (
        <div className="mb-4 bg-blue-900/50 border border-blue-600 rounded-lg p-3">
          <div className="flex items-center">
            <div className="text-blue-400 text-lg mr-2">🔑</div>
            <div>
              <div className="text-blue-200 font-bold text-sm">Temporary Access</div>
              <div className="text-blue-300 text-xs">
                You are accessing this account via temporary access.
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Admin Currently Accessing Indicator (for real client) */}
      {!clientUser.isTemporaryAccess && adminCurrentlyAccessing && (
        <div className="mb-4 bg-yellow-900/50 border border-yellow-600 rounded-lg p-3">
          <div className="flex items-center">
            <div className="text-yellow-400 text-lg mr-2">👁️</div>
            <div>
              <div className="text-yellow-200 font-bold text-sm">Admin Currently Accessing Your Account</div>
              <div className="text-yellow-300 text-xs">
                An administrator is currently accessing your account via temporary access you granted.
                {adminAccessStartTime && (
                  <span className="block mt-1">
                    Started: {new Date(adminAccessStartTime).toLocaleString()}
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Admin Access Ended Notification (for real client) */}
      {!clientUser.isTemporaryAccess && adminAccessEndNotification && (
        <div className="mb-4 bg-green-900/50 border border-green-600 rounded-lg p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="text-green-400 text-lg mr-2">✅</div>
              <div>
                <div className="text-green-200 font-bold text-sm">Admin Access Session Ended</div>
                <div className="text-green-300 text-xs">
                  Administrator has ended their access to your account.
                  <div className="mt-1">
                    <div>Started: {new Date(adminAccessEndNotification.startTime).toLocaleString()}</div>
                    <div>Ended: {new Date(adminAccessEndNotification.endTime).toLocaleString()}</div>
                    <div>Duration: {Math.round((new Date(adminAccessEndNotification.endTime).getTime() - new Date(adminAccessEndNotification.startTime).getTime()) / (1000 * 60))} minutes</div>
                  </div>
                </div>
              </div>
            </div>
            <button
              onClick={() => setAdminAccessEndNotification(null)}
              className="text-green-400 hover:text-green-300 text-xs px-2 py-1 rounded"
            >
              ✕
            </button>
          </div>
        </div>
      )}

      {/* Debug: Manual check button (remove in production) */}
      {!clientUser.isTemporaryAccess && (
        <div className="mb-4 space-y-2">
          <div>
            <button
              onClick={checkAdminAccess}
              className="px-3 py-1 bg-gray-600 text-white rounded text-xs hover:bg-gray-700 mr-2"
            >
              🔍 Check Admin Access (Debug)
            </button>
            <button
              onClick={async () => {
                console.log('🧪 Testing database update...');
                try {
                  const { data: testUpdate, error: testError } = await supabase
                    .from('temporary_access')
                    .update({ admin_currently_accessing: true })
                    .eq('client_username', clientUser.username)
                    .select();
                  console.log('🧪 Test update result:', { testUpdate, testError });
                } catch (error) {
                  console.error('🧪 Test update failed:', error);
                }
              }}
              className="px-3 py-1 bg-red-600 text-white rounded text-xs hover:bg-red-700"
            >
              🧪 Test DB Update
            </button>
          </div>
          <div className="text-xs text-gray-400">
            Status: {adminCurrentlyAccessing ? '🚨 Admin Accessing' : '✅ No Admin Access'}
          </div>
        </div>
      )}

      <div className="flex justify-between items-center mb-4">
        <div>
          <h1 className="text-white text-xl font-bold">Client Portal</h1>
          <p className="text-gray-300">Welcome, {clientUser.username}! ({clientUser.email})</p>
        </div>
        <button
          onClick={handleLogout}
          className="px-4 py-2 bg-red-600 text-white rounded-lg font-semibold shadow hover:bg-red-700 transition text-xs"
        >
          {clientUser?.isTemporaryAccess ? 'End Admin Access' : 'Logout'}
        </button>
      </div>

      {/* Notifications for completed deletion requests */}
      {completedRequests.length > 0 && (
        <div className="mb-4 p-3 bg-blue-900 rounded border border-blue-700">
          <h3 className="text-sm font-semibold text-blue-200 mb-2">Recent Deletion Request Updates:</h3>
          {completedRequests.map((request) => (
            <div key={request.id} className="text-xs text-blue-100 mb-1">
              Your deletion request for {request.item_type} was <span className={request.status === 'approved' ? 'text-green-300' : 'text-red-300'}>{request.status}</span> by {request.target_username === 'admin' ? 'Admin' : request.target_username}
              {request.status === 'approved' && ' - Item has been removed from both accounts'}
            </div>
          ))}
          <button
            onClick={() => setCompletedRequests([])}
            className="text-xs text-blue-300 underline mt-1"
          >
            Clear notifications
          </button>
        </div>
      )}

      {/* Data Sync Status */}
      <SyncStatusIndicator />


      {/* Project Management Section */}
      <div className="mt-8">
        <h2 className="text-white text-base mb-2 font-bold">Project Management</h2>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">

          {/* Project List */}
          <div className="lg:col-span-1">
            <h3 className="text-white text-sm mb-2 font-semibold">Your Projects</h3>
            <div className="bg-gray-800 rounded p-2 max-h-96 overflow-y-auto">
              {loadingProjects ? (
                <div className="text-gray-400">Loading projects...</div>
              ) : projects.length === 0 ? (
                <div className="text-gray-400">No projects yet. Admin will create projects for you.</div>
              ) : (
                projects.map((project) => (
                  <div
                    key={project.id}
                    className={`mb-2 p-3 rounded cursor-pointer transition-colors ${
                      selectedProject?.id === project.id
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-700 hover:bg-gray-600 text-gray-200'
                    }`}
                    onClick={() => setSelectedProject(project)}
                  >
                    <div className="font-semibold text-sm">{project.name}</div>
                    {project.description && (
                      <div className="text-xs opacity-75 mt-1">{project.description}</div>
                    )}
                    <div className="text-xs opacity-60 mt-1">
                      Status: {project.status} • Created: {new Date(project.created_at).toLocaleDateString()}
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Selected Project Details */}
          <div className="lg:col-span-2">
            {selectedProject ? (
              <div>
                <h3 className="text-white text-sm mb-2 font-semibold">
                  {selectedProject.name} - Messages & Files
                </h3>

                <div className="space-y-4">
                  {/* Project Messages */}
                  <div>
                    <h4 className="text-white text-xs mb-2 font-medium">Messages ({messages.length})</h4>
                    <div className="bg-gray-800 rounded p-4 max-h-96 overflow-y-auto overflow-x-hidden mb-2">
                      {messages.length === 0 ? (
                        <div className="text-gray-400 text-xs">No messages in this project.</div>
                      ) : (
                        messages.map((msg, i) => (
                          <div key={i} className={`mb-3 ${msg.sender_username === 'admin' ? 'text-right' : 'text-left'} relative`}>
                            <div className={`inline-block p-3 rounded max-w-xs ${
                              msg.sender_username === 'admin'
                                ? 'bg-blue-600 text-white'
                                : 'bg-gray-700 text-gray-200'
                            }`}>
                              <div className="text-xs pr-6">{msg.content}</div>
                              <div className="text-xs opacity-75 mt-1">
                                {new Date(msg.created_at).toLocaleString()}
                              </div>
                            </div>

                            {/* Dropdown Menu Button - In corner but visible */}
                            <div className={`absolute top-1 dropdown-container ${
                              msg.sender_username === clientUser?.username ? 'right-1' : 'left-1'
                            }`}>
                                <button
                                  onClick={() => setOpenMessageDropdown(openMessageDropdown === msg.id ? null : msg.id)}
                                  className="text-[10px] opacity-60 hover:opacity-100 p-0.5 hover:bg-gray-700 rounded"
                                >
                                  ⋯
                                </button>

                                {/* Dropdown Menu */}
                                {openMessageDropdown === msg.id && (
                                  <div className={`absolute ${
                                    msg.sender_username === clientUser?.username ? 'right-0' : 'left-0'
                                  } top-6 bg-gray-900 border border-gray-700 rounded-md shadow-xl z-[9999] py-0.5 min-w-[120px]`}>
                                    {(() => {
                                      const isWithin30Seconds = DeletionManager.isWithinErrorCorrectionWindow(msg.created_at);
                                      const isSentByCurrentUser = msg.sender_username === clientUser?.username;

                                      if (isSentByCurrentUser && isWithin30Seconds) {
                                        // Within 30 seconds: only show "Remove for everyone"
                                        return (
                                          <>
                                            <button
                                              onClick={() => {
                                                copyMessage(msg.content);
                                                setOpenMessageDropdown(null);
                                              }}
                                              className="w-full text-left px-2 py-1 text-[10px] hover:bg-gray-800 text-gray-200"
                                            >
                                              Copy
                                            </button>
                                            <button
                                              onClick={() => {
                                                deleteMessage(msg.id, true);
                                                setOpenMessageDropdown(null);
                                              }}
                                              className="w-full text-left px-2 py-1 text-[10px] hover:bg-gray-800 text-red-300"
                                            >
                                              Remove for everyone
                                            </button>
                                          </>
                                        );
                                      } else {
                                        // After 30 seconds or not sent by user: show other options
                                        return (
                                          <>
                                            <button
                                              onClick={() => {
                                                copyMessage(msg.content);
                                                setOpenMessageDropdown(null);
                                              }}
                                              className="w-full text-left px-2 py-1 text-[10px] hover:bg-gray-800 text-gray-200"
                                            >
                                              Copy
                                            </button>
                                            <button
                                              onClick={() => {
                                                deleteMessage(msg.id, false);
                                                setOpenMessageDropdown(null);
                                              }}
                                              className="w-full text-left px-2 py-1 text-[10px] hover:bg-gray-800 text-gray-200"
                                            >
                                              Remove for me
                                            </button>
                                            <button
                                              onClick={() => {
                                                const reason = prompt('Reason for deletion request (optional):');
                                                if (reason !== null) {
                                                  requestDeletion('message', msg.id, reason);
                                                }
                                                setOpenMessageDropdown(null);
                                              }}
                                              className="w-full text-left px-2 py-1 text-[10px] hover:bg-gray-800 text-blue-300"
                                            >
                                              Request deletion
                                            </button>
                                          </>
                                        );
                                      }
                                    })()}
                                  </div>
                                )}
                            </div>
                          </div>
                        ))
                      )}
                      <div ref={messagesEndRef} />
                    </div>

                    {/* Message Form */}
                    {!clientUser.isTemporaryAccess && (
                      <form onSubmit={sendMessage} className="mb-4">
                        <div className="flex mb-2">
                          <input
                            type="text"
                            value={newMessage}
                            onChange={e => {
                              setNewMessage(e.target.value);
                              // Clear pending message when user starts typing new message
                              if (pendingMessage && e.target.value.trim()) {
                                setPendingMessage('');
                              }
                            }}
                            onPaste={async (e) => {
                              // Handle file paste
                              const items = e.clipboardData?.items;
                              if (items) {
                                for (let i = 0; i < items.length; i++) {
                                  const item = items[i];
                                  if (item.kind === 'file') {
                                    e.preventDefault();
                                    const file = item.getAsFile();
                                    if (file) {
                                      setMessageAttachment(file);
                                      alert(`✅ File "${file.name}" pasted and ready to send!`);
                                    }
                                    break;
                                  }
                                }
                              }
                            }}
                            className="flex-1 px-2 py-1 rounded bg-gray-800 text-gray-100 border border-gray-700 mr-2 text-xs"
                            placeholder="Type a message or paste a file..."
                          />
                          <input
                            type="file"
                            onChange={(e) => setMessageAttachment(e.target.files?.[0] || null)}
                            className="hidden"
                            id="message-file-input"
                          />
                          <label
                            htmlFor="message-file-input"
                            className="bg-gray-600 text-white px-2 py-1 rounded font-semibold mr-2 cursor-pointer hover:bg-gray-700 transition text-xs"
                          >
                            📎
                          </label>
                          <button
                            type="submit"
                            className={`px-3 py-1 rounded font-semibold transition text-xs ${
                              pendingMessage
                                ? 'bg-yellow-600 text-white hover:bg-yellow-700'
                                : 'bg-blue-600 text-white hover:bg-blue-700'
                            }`}
                          >
                            {pendingMessage ? 'Retry' : 'Send'}
                          </button>
                        </div>
                      {messageAttachment && (
                        <div className="flex items-center justify-between bg-gray-700 rounded p-2 mb-2">
                          <span className="text-gray-200 text-xs">📎 {messageAttachment.name} ({Math.round(messageAttachment.size / 1024)} KB)</span>
                          <button
                            type="button"
                            onClick={() => setMessageAttachment(null)}
                            className="text-red-400 hover:text-red-300 text-xs"
                          >
                            Remove
                          </button>
                        </div>
                      )}
                    </form>
                    )}
                  </div>

                  {/* Project Files */}
                  <div>
                    <h4 className="text-white text-xs mb-2 font-medium">Files ({files.length})</h4>
                    <div className="bg-gray-800 rounded p-4 max-h-96 overflow-y-auto overflow-x-hidden mb-2">
                      {files.length === 0 ? (
                        <div className="text-gray-400 text-xs">No files in this project.</div>
                      ) : (
                        files.map((file, i) => (
                          <div key={i} className="mb-3 p-3 bg-gray-700 rounded relative pr-10">
                            <div className="flex justify-between items-start">
                              <div className="flex-1">
                                <div className="text-gray-200 text-xs font-medium">{file.filename}</div>
                                <div className="text-gray-400 text-xs">
                                  {file.file_size ? `${Math.round(file.file_size / 1024)} KB` : ''} •
                                  {new Date(file.created_at).toLocaleString()}
                                  {file.uploaded_by_admin ? ' • Uploaded by Admin' : ' • Uploaded by You'}
                                </div>
                                {file.admin_comment && (
                                  <div className="mt-1 p-1 bg-gray-600 rounded text-xs">
                                    <span className="text-blue-400">Admin Comment:</span> {file.admin_comment}
                                  </div>
                                )}
                                {file.client_comment && (
                                  <div className="mt-1 p-1 bg-gray-600 rounded text-xs">
                                    <span className="text-green-400">Your Comment:</span> {file.client_comment}
                                  </div>
                                )}
                              </div>

                              {/* Dropdown Menu Button */}
                              <div className="absolute top-2 right-2 dropdown-container">
                                <button
                                  onClick={() => setOpenFileDropdown(openFileDropdown === file.id ? null : file.id)}
                                  className="text-[10px] opacity-60 hover:opacity-100 p-0.5 text-gray-300 hover:bg-gray-700 rounded"
                                >
                                  ⋯
                                </button>

                                {/* Dropdown Menu */}
                                {openFileDropdown === file.id && (
                                  <div className="absolute right-0 top-6 bg-gray-900 border border-gray-700 rounded-md shadow-xl z-[9999] py-0.5 min-w-[120px]">
                                    {(() => {
                                      const isWithin30Seconds = DeletionManager.isWithinErrorCorrectionWindow(file.created_at);
                                      const isUploadedByCurrentUser = file.uploader_username === clientUser?.username;

                                      if (isUploadedByCurrentUser && isWithin30Seconds) {
                                        // Within 30 seconds: only show "Remove for everyone"
                                        return (
                                          <>
                                            <button
                                              onClick={() => {
                                                copyFile(file);
                                                setOpenFileDropdown(null);
                                              }}
                                              className="w-full text-left px-2 py-1 text-[10px] hover:bg-gray-800 text-gray-200"
                                            >
                                              Copy
                                            </button>
                                            <button
                                              onClick={() => {
                                                deleteFile(file.id, true);
                                                setOpenFileDropdown(null);
                                              }}
                                              className="w-full text-left px-2 py-1 text-[10px] hover:bg-gray-800 text-red-300"
                                            >
                                              Remove for everyone
                                            </button>
                                          </>
                                        );
                                      } else {
                                        // After 30 seconds or not uploaded by user: show other options
                                        return (
                                          <>
                                            <button
                                              onClick={() => {
                                                copyFile(file);
                                                setOpenFileDropdown(null);
                                              }}
                                              className="w-full text-left px-2 py-1 text-[10px] hover:bg-gray-800 text-gray-200"
                                            >
                                              Copy
                                            </button>
                                            <button
                                              onClick={() => {
                                                deleteFile(file.id, false);
                                                setOpenFileDropdown(null);
                                              }}
                                              className="w-full text-left px-2 py-1 text-[10px] hover:bg-gray-800 text-gray-200"
                                            >
                                              Remove for me
                                            </button>
                                            <button
                                              onClick={() => {
                                                const reason = prompt('Reason for deletion request (optional):');
                                                if (reason !== null) {
                                                  requestDeletion('file', file.id, reason);
                                                }
                                                setOpenFileDropdown(null);
                                              }}
                                              className="w-full text-left px-2 py-1 text-[10px] hover:bg-gray-800 text-blue-300"
                                            >
                                              Request deletion
                                            </button>
                                          </>
                                        );
                                      }
                                    })()}
                                  </div>
                                )}
                              </div>
                            </div>

                            {/* Client commenting for files */}
                            {!file.client_comment && !clientUser.isTemporaryAccess && (
                              <div className="mt-2 flex gap-2">
                                <input
                                  type="text"
                                  value={fileComments[file.id] || ''}
                                  onChange={(e) => setFileComments(prev => ({ ...prev, [file.id]: e.target.value }))}
                                  placeholder="Add a comment..."
                                  className="flex-1 px-2 py-1 rounded bg-gray-600 text-gray-100 border border-gray-500 text-xs"
                                />
                                <button
                                  onClick={() => addFileComment(file.id)}
                                  className="px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700 text-xs"
                                >
                                  Comment
                                </button>
                              </div>
                            )}
                          </div>
                        ))
                      )}
                      <div ref={filesEndRef} />
                    </div>

                    {/* File Upload */}
                    {!clientUser.isTemporaryAccess && (
                      <form onSubmit={async (e) => {
                        e.preventDefault();
                        if (selectedFile) {
                          const fakeEvent = {
                            target: { files: [selectedFile] }
                          } as any;
                          await handleFileUpload(fakeEvent);
                          setSelectedFile(null);
                          // Reset the file input to show "Choose File"
                          if (fileInputRef.current) {
                            fileInputRef.current.value = '';
                          }
                        }
                      }} className="mb-2">
                        <div className="flex gap-2">
                          <input
                            ref={fileInputRef}
                            type="file"
                            onChange={(e) => e.target.files && e.target.files[0] && setSelectedFile(e.target.files[0])}
                            className="flex-1 text-xs"
                          />
                          <button
                            type="submit"
                            disabled={!selectedFile || uploading}
                            className="px-3 py-1 bg-green-600 text-white rounded font-semibold hover:bg-green-700 transition disabled:opacity-50 text-xs"
                          >
                            {uploading ? 'Uploading...' : 'Upload'}
                          </button>
                        </div>
                      </form>
                    )}
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-gray-400 text-sm">Select a project to view messages and files.</div>
            )}
          </div>
        </div>
      </div>



      {/* Temporary Admin Access */}
      <div className="mb-6">
        <h2 className="text-white text-base mb-2 font-bold">Admin Access</h2>
        <div className="bg-gray-800 rounded p-4">
          {currentAccess ? (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-green-400 text-sm font-medium">✅ Admin has temporary access</div>
                  <div className="text-gray-300 text-xs mt-1">
                    Expires: {new Date(currentAccess.expires_at).toLocaleDateString()} at {new Date(currentAccess.expires_at).toLocaleTimeString()}
                  </div>
                  <div className="text-gray-400 text-xs">
                    Granted: {new Date(currentAccess.granted_by_client_at).toLocaleDateString()}
                  </div>
                </div>
                {/* Only show revoke button if this is not admin in temporary access mode */}
                {!clientUser.isTemporaryAccess && (
                  <button
                    onClick={revokeTemporaryAccess}
                    className="px-3 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700"
                  >
                    Revoke Access
                  </button>
                )}
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              <div className="text-gray-300 text-sm">
                Grant temporary access to admin for support purposes
              </div>

              {/* Show recently revoked access information */}
              {recentlyRevokedAccess && (
                <div className="bg-red-900/30 border border-red-600/50 rounded p-3 mb-3">
                  <div className="text-red-400 text-sm font-medium">❌ Access Recently Revoked</div>
                  <div className="text-red-300 text-xs mt-1">
                    Revoked by: <span className="font-medium">{recentlyRevokedAccess.revoked_by === 'client' ? 'You' : 'Admin'}</span>
                  </div>
                  <div className="text-red-300 text-xs">
                    Revoked: {new Date(recentlyRevokedAccess.revoked_at).toLocaleString()}
                  </div>
                  <div className="text-red-300 text-xs">
                    Original expiration: {new Date(recentlyRevokedAccess.expires_at).toLocaleString()}
                  </div>
                </div>
              )}

              <button
                onClick={() => setShowAccessDialog(true)}
                className="px-4 py-2 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
              >
                Grant Admin Access
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Analytics placeholder */}
      <div className="mb-2">
        <h2 className="text-white text-base mb-2 font-bold">Analytics</h2>
        <div className="bg-gray-800 rounded p-2 text-gray-400">(Coming soon...)</div>
      </div>

      {/* Temporary Access Dialog */}
      {showAccessDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-900 rounded-lg p-6 max-w-md w-full mx-4 border border-gray-700">
            <h3 className="text-white text-lg font-bold mb-4">Grant Admin Access</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-gray-300 text-sm mb-2">Access Duration</label>
                <select
                  value={accessDays}
                  onChange={(e) => setAccessDays(parseInt(e.target.value))}
                  className="w-full px-3 py-2 bg-gray-800 text-gray-100 border border-gray-700 rounded"
                >
                  <option value={1}>1 Day</option>
                  <option value={3}>3 Days</option>
                  <option value={7}>7 Days (1 Week)</option>
                  <option value={14}>14 Days (2 Weeks)</option>
                  <option value={30}>30 Days (1 Month)</option>
                </select>
              </div>

              <div className="bg-yellow-900/50 border border-yellow-600 rounded p-3">
                <div className="text-yellow-200 text-sm font-medium mb-2">⚠️ Are you sure?</div>
                <div className="text-yellow-300 text-xs space-y-1">
                  <div>• Admin will be able to access your account for {accessDays} day{accessDays !== 1 ? 's' : ''} (unless revoked)</div>
                  <div>• They can view your messages and files</div>
                  <div>• Admin cannot remove messages or files</div>
                  <div>• Admin cannot give themselves extended access</div>
                  <div>• Admin does not have access to your password</div>
                  <div>• You can revoke access at any time</div>
                  <div>• Access expires automatically on {new Date(Date.now() + accessDays * 24 * 60 * 60 * 1000).toLocaleDateString()}</div>
                </div>
              </div>

              <div className="flex gap-3">
                <button
                  onClick={() => setShowAccessDialog(false)}
                  className="flex-1 px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
                  disabled={grantingAccess}
                >
                  Cancel
                </button>
                <button
                  onClick={grantTemporaryAccess}
                  disabled={grantingAccess}
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
                >
                  {grantingAccess ? 'Granting...' : 'Yes, Grant Access'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Reason Popup Modal */}
      {showReasonPopup && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 p-6 rounded-lg max-w-md w-full mx-4">
            <h3 className="text-white text-lg font-bold mb-4">Deletion Request Reason</h3>
            <div className="bg-gray-700 p-3 rounded mb-4">
              <p className="text-gray-200 text-sm whitespace-pre-wrap">
                {selectedReason || 'No reason provided'}
              </p>
            </div>
            <button
              onClick={() => {
                setShowReasonPopup(false);
                setSelectedReason('');
              }}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Close
            </button>
          </div>
        </div>
      )}
      </div>
    </div>
  );
}

export default ClientPortal;
